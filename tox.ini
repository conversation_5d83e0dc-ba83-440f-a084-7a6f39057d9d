[tox]
envlist =
    py38
    style
    lint
    typing
    docbuild

[testenv]:
deps =
    pytest
    Sybil
    pytest-cov
    jsonschema
commands = pytest --cov --cov-report=xml

[testenv:lint]
skip_install = true
deps =
    pylint
commands = pylint src

[testenv:typing]
skip_install = true
deps =
    mypy
    pyright
    types-python-dateutil
commands =
    mypy --check-untyped-defs src tests/check_typing.py
    pyright src tests/check_typing.py

[testenv:docdev]
description = Run the documentation development server.
skip_install = true
deps =
    -r requirements/docs.txt
    sphinx-autobuild
commands = sphinx-autobuild docs docs/_build

[testenv:docbuild]
skip_install = true
deps =
    -r requirements/docs.txt
commands = sphinx-build -W -E -a docs docs/_build

[testenv:style]
deps = pre-commit
skip_install = true
commands = pre-commit run --all-files --show-diff-on-failure

[testenv:coverage-html]
depends = py38
skip_install = true
deps = coverage
commands = coverage html